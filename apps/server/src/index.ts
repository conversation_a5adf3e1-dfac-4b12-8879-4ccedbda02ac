import "dotenv/config";
import { OpenAP<PERSON><PERSON>andler } from "@orpc/openapi/fetch";
import { OpenAPIReferencePlugin } from "@orpc/openapi/plugins";
import { onError } from "@orpc/server";
import { RPCHandler } from "@orpc/server/fetch";
import { ZodToJsonSchemaConverter } from "@orpc/zod/zod4";
import { Hono } from "hono";
import { cors } from "hono/cors";
import { logger } from "hono/logger";
import { auth } from "./lib/auth";
import { createContext } from "./lib/context";
import { isEventProcessed, verifyWebhookSignature } from "./lib/webhook";
import { appRouter } from "./routers/index";
import { webhookProcessor } from "./services/webhook-processor";

const app = new Hono();

app.use(logger());
app.use(
	"/*",
	cors({
		origin: process.env.CORS_ORIGIN || "",
		allowMethods: ["GET", "POST", "OPTIONS"],
		allowHeaders: ["Content-Type", "Authorization"],
		credentials: true,
	}),
);

app.on(["POST", "GET"], "/api/auth/**", (c) => auth.handler(c.req.raw));

// Stripe webhook endpoint - must be before the catch-all middleware
app.post("/webhooks/stripe", async (c) => {
	try {
		// Get the raw body for signature verification
		const body = await c.req.text();
		const signature = c.req.header("stripe-signature");

		if (!signature) {
			console.error("Missing Stripe signature header");
			return c.json({ error: "Missing signature" }, 400);
		}

		const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;
		if (!endpointSecret) {
			console.error("Missing STRIPE_WEBHOOK_SECRET environment variable");
			return c.json({ error: "Webhook not configured" }, 500);
		}

		// Verify the webhook signature
		const event = verifyWebhookSignature(body, signature, endpointSecret);

		// Check if we've already processed this event (idempotency)
		if (await isEventProcessed(event.id)) {
			console.log(`Event ${event.id} already processed, skipping`);
			return c.json({ received: true, message: "Event already processed" });
		}

		// Process the event asynchronously
		// We return success immediately to Stripe, then process in background
		setImmediate(async () => {
			try {
				await webhookProcessor.processEvent(event);
			} catch (error) {
				console.error(
					`Background processing failed for event ${event.id}:`,
					error,
				);
			}
		});

		return c.json({ received: true });
	} catch (error) {
		console.error("Webhook processing error:", error);
		return c.json(
			{ error: error instanceof Error ? error.message : "Unknown error" },
			400,
		);
	}
});

export const apiHandler = new OpenAPIHandler(appRouter, {
	plugins: [
		new OpenAPIReferencePlugin({
			schemaConverters: [new ZodToJsonSchemaConverter()],
		}),
	],
	interceptors: [
		onError((error) => {
			console.error(error);
		}),
	],
});

export const rpcHandler = new RPCHandler(appRouter, {
	interceptors: [
		onError((error) => {
			console.error(error);
		}),
	],
});

app.use("/*", async (c, next) => {
	const context = await createContext({ context: c });

	const rpcResult = await rpcHandler.handle(c.req.raw, {
		prefix: "/rpc",
		context: context,
	});

	if (rpcResult.matched) {
		return c.newResponse(rpcResult.response.body, rpcResult.response);
	}

	const apiResult = await apiHandler.handle(c.req.raw, {
		prefix: "/api",
		context: context,
	});

	if (apiResult.matched) {
		return c.newResponse(apiResult.response.body, apiResult.response);
	}

	await next();
});

app.get("/", (c) => {
	return c.text("OK");
});

export default app;
