import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import type Stripe from "stripe";
import prisma from "@/db";
import {
	activateLicensesForPayment,
	getPaymentIntentByStripeId,
	isEventProcessed,
	markEventProcessed,
	processDeviceExpansion,
	recordWebhookEvent,
	updatePaymentIntentStatus,
	verifyWebhookSignature,
} from "@/lib/webhook";

// Mock Stripe
const mockStripe = {
	webhooks: {
		constructEvent: jest.fn(),
	},
};

jest.mock("@/lib/stripe", () => ({
	stripe: mockStripe,
}));

describe("Webhook Utilities", () => {
	beforeEach(() => {
		jest.clearAllMocks();
	});

	describe("verifyWebhookSignature", () => {
		it("should verify valid webhook signature", () => {
			const mockEvent = {
				id: "evt_test",
				type: "payment_intent.succeeded",
			} as Stripe.Event;
			mockStripe.webhooks.constructEvent.mockReturnValue(mockEvent);

			const result = verifyWebhookSignature("payload", "signature", "secret");

			expect(mockStripe.webhooks.constructEvent).toHaveBeenCalledWith(
				"payload",
				"signature",
				"secret",
			);
			expect(result).toEqual(mockEvent);
		});

		it("should throw error for invalid signature", () => {
			mockStripe.webhooks.constructEvent.mockImplementation(() => {
				throw new Error("Invalid signature");
			});

			expect(() => {
				verifyWebhookSignature("payload", "invalid_signature", "secret");
			}).toThrow(
				"Webhook signature verification failed: Error: Invalid signature",
			);
		});
	});

	describe("isEventProcessed", () => {
		it("should return true for processed event", async () => {
			(prisma.webhookEvent.findUnique as jest.Mock).mockResolvedValue({
				id: "webhook_1",
				processed: true,
			});

			const result = await isEventProcessed("evt_test");

			expect(prisma.webhookEvent.findUnique).toHaveBeenCalledWith({
				where: { stripeEventId: "evt_test" },
			});
			expect(result).toBe(true);
		});

		it("should return false for unprocessed event", async () => {
			(prisma.webhookEvent.findUnique as jest.Mock).mockResolvedValue({
				id: "webhook_1",
				processed: false,
			});

			const result = await isEventProcessed("evt_test");
			expect(result).toBe(false);
		});

		it("should return false for non-existent event", async () => {
			(prisma.webhookEvent.findUnique as jest.Mock).mockResolvedValue(null);

			const result = await isEventProcessed("evt_test");
			expect(result).toBe(false);
		});
	});

	describe("recordWebhookEvent", () => {
		it("should record webhook event", async () => {
			const mockEvent = {
				id: "evt_test",
				type: "payment_intent.succeeded",
			} as Stripe.Event;

			const mockCreatedEvent = {
				id: "webhook_1",
				stripeEventId: "evt_test",
				eventType: "payment_intent.succeeded",
			};

			(prisma.webhookEvent.create as jest.Mock).mockResolvedValue(
				mockCreatedEvent,
			);

			const result = await recordWebhookEvent(mockEvent, "payment_1");

			expect(prisma.webhookEvent.create).toHaveBeenCalledWith({
				data: {
					stripeEventId: "evt_test",
					eventType: "payment_intent.succeeded",
					processed: false,
					paymentIntentId: "payment_1",
				},
			});
			expect(result).toEqual(mockCreatedEvent);
		});
	});

	describe("markEventProcessed", () => {
		it("should mark event as successfully processed", async () => {
			await markEventProcessed("evt_test", true);

			expect(prisma.webhookEvent.update).toHaveBeenCalledWith({
				where: { stripeEventId: "evt_test" },
				data: {
					processed: true,
					processedAt: expect.any(Date),
					errorMessage: null,
					retryCount: 0,
				},
			});
		});

		it("should mark event as failed with error message", async () => {
			await markEventProcessed("evt_test", false, "Test error");

			expect(prisma.webhookEvent.update).toHaveBeenCalledWith({
				where: { stripeEventId: "evt_test" },
				data: {
					processed: false,
					processedAt: null,
					errorMessage: "Test error",
					retryCount: { increment: 1 },
				},
			});
		});
	});

	describe("getPaymentIntentByStripeId", () => {
		it("should return payment intent with relations", async () => {
			const mockPaymentIntent = {
				id: "payment_1",
				stripePaymentIntentId: "pi_test",
				licenses: [],
				deviceExpansions: [],
			};

			(prisma.paymentIntent.findUnique as jest.Mock).mockResolvedValue(
				mockPaymentIntent,
			);

			const result = await getPaymentIntentByStripeId("pi_test");

			expect(prisma.paymentIntent.findUnique).toHaveBeenCalledWith({
				where: { stripePaymentIntentId: "pi_test" },
				include: {
					licenses: true,
					deviceExpansions: true,
				},
			});
			expect(result).toEqual(mockPaymentIntent);
		});
	});

	describe("updatePaymentIntentStatus", () => {
		it("should update payment status to succeeded", async () => {
			const mockDate = new Date();
			jest.spyOn(global, "Date").mockImplementation(() => mockDate as any);

			await updatePaymentIntentStatus("pi_test", "SUCCEEDED");

			expect(prisma.paymentIntent.update).toHaveBeenCalledWith({
				where: { stripePaymentIntentId: "pi_test" },
				data: {
					status: "SUCCEEDED",
					processedAt: mockDate,
				},
			});
		});

		it("should update payment status to failed without processedAt", async () => {
			await updatePaymentIntentStatus("pi_test", "FAILED");

			expect(prisma.paymentIntent.update).toHaveBeenCalledWith({
				where: { stripePaymentIntentId: "pi_test" },
				data: {
					status: "FAILED",
					processedAt: null,
				},
			});
		});
	});

	describe("activateLicensesForPayment", () => {
		it("should activate existing licenses", async () => {
			const mockPaymentIntent = {
				id: "payment_1",
				paymentType: "LICENSE_PURCHASE",
				amount: 2999,
				customerEmail: "<EMAIL>",
				customerName: "Test User",
				licenses: [{ id: "license_1" }],
			};

			(prisma.paymentIntent.findUnique as jest.Mock).mockResolvedValue(
				mockPaymentIntent,
			);

			await activateLicensesForPayment("payment_1");

			expect(prisma.license.updateMany).toHaveBeenCalledWith({
				where: { paymentIntentId: "payment_1" },
				data: {
					status: "ACTIVE",
					activatedAt: expect.any(Date),
				},
			});
		});

		it("should create new license for license purchase", async () => {
			const mockPaymentIntent = {
				id: "payment_1",
				paymentType: "LICENSE_PURCHASE",
				amount: 2999,
				customerEmail: "<EMAIL>",
				customerName: "Test User",
				licenses: [],
			};

			(prisma.paymentIntent.findUnique as jest.Mock).mockResolvedValue(
				mockPaymentIntent,
			);

			// Mock generateLicenseKey
			jest.mock("@/lib/helpers", () => ({
				generateLicenseKey: jest.fn().mockReturnValue("TEST-LICENSE-KEY"),
			}));

			await activateLicensesForPayment("payment_1");

			expect(prisma.license.create).toHaveBeenCalledWith({
				data: expect.objectContaining({
					licenseType: "PRO",
					status: "ACTIVE",
					maxDevices: 2,
					customerEmail: "<EMAIL>",
					customerName: "Test User",
					paymentIntentId: "payment_1",
					totalPaidAmount: 2999,
				}),
			});
		});

		it("should throw error for non-existent payment intent", async () => {
			(prisma.paymentIntent.findUnique as jest.Mock).mockResolvedValue(null);

			await expect(activateLicensesForPayment("payment_1")).rejects.toThrow(
				"Payment intent not found: payment_1",
			);
		});
	});

	describe("processDeviceExpansion", () => {
		it("should process device expansions", async () => {
			const mockExpansions = [
				{
					id: "expansion_1",
					licenseId: "license_1",
					additionalDevices: 3,
					license: { id: "license_1" },
				},
			];

			(prisma.deviceExpansion.findMany as jest.Mock).mockResolvedValue(
				mockExpansions,
			);

			await processDeviceExpansion("payment_1");

			expect(prisma.deviceExpansion.update).toHaveBeenCalledWith({
				where: { id: "expansion_1" },
				data: {
					status: "PROCESSED",
					processedAt: expect.any(Date),
				},
			});

			expect(prisma.license.update).toHaveBeenCalledWith({
				where: { id: "license_1" },
				data: {
					maxDevices: { increment: 3 },
				},
			});
		});
	});
});

describe("WebhookProcessor", () => {
	let webhookProcessor: any;

	beforeEach(async () => {
		jest.clearAllMocks();
		// Dynamic import to avoid module loading issues
		const { WebhookProcessor } = await import("@/services/webhook-processor");
		webhookProcessor = new WebhookProcessor();
	});

	describe("processEvent", () => {
		it("should process payment_intent.succeeded event", async () => {
			const mockEvent = {
				id: "evt_test",
				type: "payment_intent.succeeded",
				data: {
					object: {
						id: "pi_test",
						amount: 2999,
						currency: "usd",
					},
				},
			} as Stripe.Event;

			const mockPaymentIntent = {
				id: "payment_1",
				paymentType: "LICENSE_PURCHASE",
				customerEmail: "<EMAIL>",
				licenses: [],
			};

			// Mock the webhook utility functions
			jest.doMock("@/lib/webhook", () => ({
				recordWebhookEvent: jest.fn(),
				markEventProcessed: jest.fn(),
				getPaymentIntentByStripeId: jest
					.fn()
					.mockResolvedValue(mockPaymentIntent),
				updatePaymentIntentStatus: jest.fn(),
				activateLicensesForPayment: jest.fn(),
				createWebhookAuditLog: jest.fn(),
			}));

			await webhookProcessor.processEvent(mockEvent);

			// Verify the event was processed
			expect(true).toBe(true); // Placeholder assertion
		});

		it("should handle processing errors", async () => {
			const mockEvent = {
				id: "evt_test",
				type: "payment_intent.succeeded",
				data: {
					object: {
						id: "pi_test",
					},
				},
			} as Stripe.Event;

			// Mock error in processing
			jest.doMock("@/lib/webhook", () => ({
				recordWebhookEvent: jest
					.fn()
					.mockRejectedValue(new Error("Database error")),
				markEventProcessed: jest.fn(),
			}));

			await expect(webhookProcessor.processEvent(mockEvent)).rejects.toThrow(
				"Database error",
			);
		});
	});
});
