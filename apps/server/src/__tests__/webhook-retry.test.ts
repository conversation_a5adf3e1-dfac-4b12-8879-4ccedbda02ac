import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import prisma from "@/db";
import { WebhookRetryService } from "@/services/webhook-retry";

// Mock Stripe
const mockStripe = {
	events: {
		retrieve: jest.fn(),
	},
};

jest.mock("@/lib/stripe", () => ({
	stripe: mockStripe,
}));

// Mock webhook processor
const mockWebhookProcessor = {
	processEvent: jest.fn(),
};

jest.mock("@/services/webhook-processor", () => ({
	webhookProcessor: mockWebhookProcessor,
}));

describe("WebhookRetryService", () => {
	let retryService: WebhookRetryService;

	beforeEach(() => {
		jest.clearAllMocks();
		retryService = new WebhookRetryService();
	});

	describe("retryFailedEvents", () => {
		it("should retry failed events", async () => {
			const mockFailedEvents = [
				{
					id: "webhook_1",
					stripeEventId: "evt_test_1",
					retryCount: 0,
					createdAt: new Date(Date.now() - 10 * 60 * 1000), // 10 minutes ago
				},
				{
					id: "webhook_2",
					stripeEventId: "evt_test_2",
					retryCount: 1,
					createdAt: new Date(Date.now() - 20 * 60 * 1000), // 20 minutes ago
				},
			];

			const mockStripeEvent = {
				id: "evt_test_1",
				type: "payment_intent.succeeded",
			};

			(prisma.webhookEvent.findMany as jest.Mock).mockResolvedValue(
				mockFailedEvents,
			);
			mockStripe.events.retrieve.mockResolvedValue(mockStripeEvent);
			mockWebhookProcessor.processEvent.mockResolvedValue(undefined);

			await retryService.retryFailedEvents();

			expect(prisma.webhookEvent.findMany).toHaveBeenCalledWith({
				where: {
					processed: false,
					retryCount: { lt: 3 },
					createdAt: { lt: expect.any(Date) },
				},
				orderBy: { createdAt: "asc" },
				take: 10,
			});

			expect(mockStripe.events.retrieve).toHaveBeenCalledWith("evt_test_1");
			expect(mockWebhookProcessor.processEvent).toHaveBeenCalledWith(
				mockStripeEvent,
			);
		});

		it("should handle no failed events", async () => {
			(prisma.webhookEvent.findMany as jest.Mock).mockResolvedValue([]);

			await retryService.retryFailedEvents();

			expect(mockStripe.events.retrieve).not.toHaveBeenCalled();
			expect(mockWebhookProcessor.processEvent).not.toHaveBeenCalled();
		});

		it("should handle retry failures", async () => {
			const mockFailedEvent = {
				id: "webhook_1",
				stripeEventId: "evt_test_1",
				retryCount: 0,
				createdAt: new Date(Date.now() - 10 * 60 * 1000),
			};

			(prisma.webhookEvent.findMany as jest.Mock).mockResolvedValue([
				mockFailedEvent,
			]);
			mockStripe.events.retrieve.mockRejectedValue(
				new Error("Stripe API error"),
			);

			await retryService.retryFailedEvents();

			expect(prisma.webhookEvent.update).toHaveBeenCalledWith({
				where: { id: "webhook_1" },
				data: {
					retryCount: { increment: 1 },
					errorMessage: "Stripe API error",
				},
			});
		});

		it("should mark as permanently failed after max retries", async () => {
			const mockFailedEvent = {
				id: "webhook_1",
				stripeEventId: "evt_test_1",
				retryCount: 2, // This will be the 3rd attempt (max)
				createdAt: new Date(Date.now() - 10 * 60 * 1000),
			};

			(prisma.webhookEvent.findMany as jest.Mock).mockResolvedValue([
				mockFailedEvent,
			]);
			mockStripe.events.retrieve.mockRejectedValue(
				new Error("Stripe API error"),
			);

			await retryService.retryFailedEvents();

			expect(prisma.webhookEvent.update).toHaveBeenCalledWith({
				where: { id: "webhook_1" },
				data: {
					retryCount: { increment: 1 },
					errorMessage: "Stripe API error",
				},
			});

			// Should also mark as permanently failed
			expect(prisma.webhookEvent.update).toHaveBeenCalledWith({
				where: { id: "webhook_1" },
				data: {
					errorMessage: "Max retries exceeded - permanently failed",
				},
			});
		});
	});

	describe("getWebhookStats", () => {
		it("should return webhook statistics", async () => {
			(prisma.webhookEvent.count as jest.Mock)
				.mockResolvedValueOnce(100) // total
				.mockResolvedValueOnce(80) // processed
				.mockResolvedValueOnce(15) // failed
				.mockResolvedValueOnce(5); // permanently failed

			const stats = await retryService.getWebhookStats();

			expect(stats).toEqual({
				total: 100,
				processed: 80,
				failed: 15,
				pending: 0,
				permanentlyFailed: 5,
			});
		});
	});

	describe("cleanupOldEvents", () => {
		it("should clean up old processed events", async () => {
			(prisma.webhookEvent.deleteMany as jest.Mock).mockResolvedValue({
				count: 25,
			});

			const result = await retryService.cleanupOldEvents();

			expect(prisma.webhookEvent.deleteMany).toHaveBeenCalledWith({
				where: {
					processed: true,
					createdAt: { lt: expect.any(Date) },
				},
			});

			expect(result).toBe(25);
		});
	});

	describe("manualRetry", () => {
		it("should manually retry a specific event", async () => {
			const mockWebhookEvent = {
				id: "webhook_1",
				stripeEventId: "evt_test_1",
				processed: false,
			};

			const mockStripeEvent = {
				id: "evt_test_1",
				type: "payment_intent.succeeded",
			};

			(prisma.webhookEvent.findUnique as jest.Mock).mockResolvedValue(
				mockWebhookEvent,
			);
			mockStripe.events.retrieve.mockResolvedValue(mockStripeEvent);
			mockWebhookProcessor.processEvent.mockResolvedValue(undefined);

			const result = await retryService.manualRetry("webhook_1");

			expect(prisma.webhookEvent.findUnique).toHaveBeenCalledWith({
				where: { id: "webhook_1" },
			});
			expect(mockStripe.events.retrieve).toHaveBeenCalledWith("evt_test_1");
			expect(mockWebhookProcessor.processEvent).toHaveBeenCalledWith(
				mockStripeEvent,
			);
			expect(result).toBe(true);
		});

		it("should throw error for non-existent event", async () => {
			(prisma.webhookEvent.findUnique as jest.Mock).mockResolvedValue(null);

			await expect(retryService.manualRetry("webhook_1")).rejects.toThrow(
				"Webhook event not found: webhook_1",
			);
		});

		it("should throw error for already processed event", async () => {
			const mockWebhookEvent = {
				id: "webhook_1",
				stripeEventId: "evt_test_1",
				processed: true,
			};

			(prisma.webhookEvent.findUnique as jest.Mock).mockResolvedValue(
				mockWebhookEvent,
			);

			await expect(retryService.manualRetry("webhook_1")).rejects.toThrow(
				"Webhook event already processed: webhook_1",
			);
		});

		it("should handle manual retry failure", async () => {
			const mockWebhookEvent = {
				id: "webhook_1",
				stripeEventId: "evt_test_1",
				processed: false,
			};

			(prisma.webhookEvent.findUnique as jest.Mock).mockResolvedValue(
				mockWebhookEvent,
			);
			mockStripe.events.retrieve.mockRejectedValue(
				new Error("Stripe API error"),
			);

			const result = await retryService.manualRetry("webhook_1");

			expect(prisma.webhookEvent.update).toHaveBeenCalledWith({
				where: { id: "webhook_1" },
				data: {
					retryCount: { increment: 1 },
					errorMessage: "Stripe API error",
				},
			});
			expect(result).toBe(false);
		});
	});
});
