import { afterAll, beforeAll, beforeEach, jest } from "@jest/globals";

// Mock environment variables
process.env.DATABASE_URL = "postgresql://test:test@localhost:5432/test_db";
process.env.STRIPE_SECRET_KEY = "sk_test_mock_key";
process.env.STRIPE_WEBHOOK_SECRET = "whsec_mock_secret";
process.env.JWT_SECRET = "test_jwt_secret";

// Mock Prisma client
jest.mock("@/db", () => ({
	__esModule: true,
	default: {
		webhookEvent: {
			findUnique: jest.fn(),
			create: jest.fn(),
			update: jest.fn(),
			findMany: jest.fn(),
			deleteMany: jest.fn(),
			count: jest.fn(),
		},
		paymentIntent: {
			findUnique: jest.fn(),
			update: jest.fn(),
			create: jest.fn(),
		},
		license: {
			create: jest.fn(),
			updateMany: jest.fn(),
			update: jest.fn(),
		},
		deviceExpansion: {
			findMany: jest.fn(),
			update: jest.fn(),
		},
		auditLog: {
			create: jest.fn(),
		},
	},
}));

// Mock Stripe
jest.mock("stripe", () => {
	return jest.fn().mockImplementation(() => ({
		webhooks: {
			constructEvent: jest.fn(),
		},
		events: {
			retrieve: jest.fn(),
		},
	}));
});

beforeAll(async () => {
	// Global test setup
});

afterAll(async () => {
	// Global test cleanup
});

beforeEach(() => {
	// Reset all mocks before each test
	jest.clearAllMocks();
});
