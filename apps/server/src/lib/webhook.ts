import type Stripe from "stripe";
import prisma from "@/db";
import { generateLicenseKey } from "./helpers";
import { stripe } from "./stripe";

/**
 * Verify Stripe webhook signature
 * @param payload - Raw request body as string
 * @param signature - Stripe signature header
 * @param endpointSecret - Webhook endpoint secret from <PERSON><PERSON>
 * @returns Verified Stripe event object
 * @throws Error if signature verification fails
 */
export function verifyWebhookSignature(
	payload: string,
	signature: string,
	endpointSecret: string,
): Stripe.Event {
	try {
		return stripe.webhooks.constructEvent(payload, signature, endpointSecret);
	} catch (error) {
		console.error("Webhook signature verification failed:", error);
		throw new Error(`Webhook signature verification failed: ${error}`);
	}
}

/**
 * Check if webhook event has already been processed
 * @param eventId - Stripe event ID
 * @returns true if event has been processed, false otherwise
 */
export async function isEventProcessed(eventId: string): Promise<boolean> {
	const existingEvent = await prisma.webhookEvent.findUnique({
		where: { stripeEventId: eventId },
	});
	return existingEvent?.processed === true;
}

/**
 * Record webhook event in database
 * @param event - Stripe event object
 * @param paymentIntentId - Optional payment intent ID to associate with event
 * @returns Created webhook event record
 */
export async function recordWebhookEvent(
	event: Stripe.Event,
	paymentIntentId?: string,
) {
	return await prisma.webhookEvent.create({
		data: {
			stripeEventId: event.id,
			eventType: event.type,
			processed: false,
			paymentIntentId,
		},
	});
}

/**
 * Mark webhook event as processed
 * @param eventId - Stripe event ID
 * @param success - Whether processing was successful
 * @param errorMessage - Error message if processing failed
 */
export async function markEventProcessed(
	eventId: string,
	success: boolean,
	errorMessage?: string,
) {
	await prisma.webhookEvent.update({
		where: { stripeEventId: eventId },
		data: {
			processed: success,
			processedAt: success ? new Date() : null,
			errorMessage: errorMessage || null,
			retryCount: success ? 0 : { increment: 1 },
		},
	});
}

/**
 * Get payment intent from database by Stripe payment intent ID
 * @param stripePaymentIntentId - Stripe payment intent ID
 * @returns Payment intent record or null if not found
 */
export async function getPaymentIntentByStripeId(
	stripePaymentIntentId: string,
) {
	return await prisma.paymentIntent.findUnique({
		where: { stripePaymentIntentId },
		include: {
			licenses: true,
			deviceExpansions: true,
		},
	});
}

/**
 * Update payment intent status
 * @param stripePaymentIntentId - Stripe payment intent ID
 * @param status - New payment status
 * @param processedAt - Optional processed timestamp
 */
export async function updatePaymentIntentStatus(
	stripePaymentIntentId: string,
	status:
		| "PENDING"
		| "PROCESSING"
		| "SUCCEEDED"
		| "FAILED"
		| "CANCELLED"
		| "REFUNDED",
	processedAt?: Date,
) {
	await prisma.paymentIntent.update({
		where: { stripePaymentIntentId },
		data: {
			status,
			processedAt: processedAt || (status === "SUCCEEDED" ? new Date() : null),
		},
	});
}

/**
 * Create and activate licenses for a successful payment
 * @param paymentIntentId - Internal payment intent ID
 */
export async function activateLicensesForPayment(paymentIntentId: string) {
	const paymentIntent = await prisma.paymentIntent.findUnique({
		where: { id: paymentIntentId },
		include: { licenses: true },
	});

	if (!paymentIntent) {
		throw new Error(`Payment intent not found: ${paymentIntentId}`);
	}

	// If licenses already exist, just activate them
	if (paymentIntent.licenses.length > 0) {
		await prisma.license.updateMany({
			where: { paymentIntentId },
			data: {
				status: "ACTIVE",
				activatedAt: new Date(),
			},
		});
		return;
	}

	// Create new license for license purchases
	if (paymentIntent.paymentType === "LICENSE_PURCHASE") {
		// Determine license type and device limits based on amount
		let licenseType: "TRIAL" | "PRO" | "ENTERPRISE" = "PRO";
		let maxDevices = 2;

		// You can customize these rules based on your pricing
		if (paymentIntent.amount <= 2999) {
			// $29.99 or less
			licenseType = "PRO";
			maxDevices = 2;
		} else if (paymentIntent.amount <= 9999) {
			// $99.99 or less
			licenseType = "PRO";
			maxDevices = 5;
		} else {
			// Enterprise pricing
			licenseType = "ENTERPRISE";
			maxDevices = 10;
		}

		await prisma.license.create({
			data: {
				licenseKey: generateLicenseKey(),
				licenseType,
				status: "ACTIVE",
				maxDevices,
				usedDevices: 0,
				activatedAt: new Date(),
				customerEmail: paymentIntent.customerEmail,
				customerName: paymentIntent.customerName,
				paymentIntentId: paymentIntent.id,
				totalPaidAmount: paymentIntent.amount,
				emailSentAt: null, // Will be set when email is sent
				emailDeliveryStatus: null,
			},
		});
	}
}

/**
 * Process device expansion for successful payment
 * @param paymentIntentId - Internal payment intent ID
 */
export async function processDeviceExpansion(paymentIntentId: string) {
	const deviceExpansions = await prisma.deviceExpansion.findMany({
		where: { paymentIntentId },
		include: { license: true },
	});

	for (const expansion of deviceExpansions) {
		// Update expansion status
		await prisma.deviceExpansion.update({
			where: { id: expansion.id },
			data: {
				status: "PROCESSED",
				processedAt: new Date(),
			},
		});

		// Update license max devices
		await prisma.license.update({
			where: { id: expansion.licenseId },
			data: {
				maxDevices: { increment: expansion.additionalDevices },
			},
		});
	}
}

/**
 * Create audit log entry for webhook processing
 * @param action - Audit action type
 * @param details - Additional details for the audit log
 * @param licenseId - Optional license ID
 * @param customerEmail - Optional customer email
 */
export async function createWebhookAuditLog(
	action:
		| "WEBHOOK_PROCESSED"
		| "WEBHOOK_FAILED"
		| "PAYMENT_SUCCEEDED"
		| "PAYMENT_FAILED",
	details: Record<string, string | number | boolean | null>,
	licenseId?: string,
	customerEmail?: string,
) {
	await prisma.auditLog.create({
		data: {
			action,
			details,
			licenseId,
			customerEmail,
		},
	});
}
